{"name": "hono-open-api-starter", "type": "module", "version": "1.0.0", "license": "MIT", "scripts": {"dev": "bun --hot src/index.ts", "start": "bun ./dist/index.js", "build": "bun build src/index.ts --outdir dist --target bun --sourcemap", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "bun run lint --fix", "test": "cross-env NODE_ENV=test vitest"}, "dependencies": {"@hono/node-server": "^1.14.3", "@hono/zod-openapi": "^0.19.8", "@libsql/client": "^0.15.8", "@scalar/hono-api-reference": "^0.9.2", "@supabase/supabase-js": "^2.52.0", "dotenv": "^17.0.1", "dotenv-expand": "^12.0.2", "drizzle-orm": "^0.33.0", "drizzle-zod": "^0.5.1", "hono": "^4.7.11", "hono-pino": "^0.9.1", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.7", "stoker": "1.4.3", "uuid": "^11.1.0", "zod": "^3.25.51"}, "devDependencies": {"@antfu/eslint-config": "^4.13.3", "@types/node": "^24.0.10", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "drizzle-kit": "^0.24.2", "eslint": "^9.28.0", "eslint-plugin-format": "^1.0.1", "tsc-alias": "^1.8.16", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.2.1"}}