import db from "@/db";
import { apiLogs } from "@/db/schema";
import type { Context } from "hono";
import type { AuthenticatedUser } from "@/middlewares/auth";

export interface LogData {
  // Request information
  method: string;
  url: string;
  path: string;
  userAgent?: string;
  ipAddress?: string;
  
  // User context
  user?: AuthenticatedUser;
  
  // Request data
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  requestBody?: any;
  
  // Response data
  statusCode?: number;
  responseBody?: any;
  responseHeaders?: Record<string, string>;
  
  // Timing
  startTime: string;
  endTime?: string;
  duration?: number;
  
  // Error information
  errorMessage?: string;
  errorStack?: string;
}

export class ApiLogger {
  /**
   * Log API request/response to database
   */
  static async log(data: LogData): Promise<void> {
    try {
      await db.insert(apiLogs).values({
        method: data.method,
        url: data.url,
        path: data.path,
        userAgent: data.userAgent,
        ipAddress: data.ipAddress,
        userId: data.user?.id,
        workspaceId: data.user?.workspace?.id,
        headers: data.headers,
        queryParams: data.queryParams,
        requestBody: data.requestBody,
        statusCode: data.statusCode,
        responseBody: data.responseBody,
        responseHeaders: data.responseHeaders,
        startTime: data.startTime,
        endTime: data.endTime,
        duration: data.duration,
        errorMessage: data.errorMessage,
        errorStack: data.errorStack,
      });
    } catch (error) {
      // Don't let logging errors break the API
      console.error('Failed to log API request:', error);
    }
  }

  /**
   * Get IP address from request
   */
  static getIpAddress(c: Context): string {
    // Check various headers for IP address
    const forwarded = c.req.header('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIp = c.req.header('x-real-ip');
    if (realIp) {
      return realIp;
    }
    
    const cfConnectingIp = c.req.header('cf-connecting-ip');
    if (cfConnectingIp) {
      return cfConnectingIp;
    }
    
    // Fallback to connection remote address
    return 'unknown';
  }

  /**
   * Sanitize request body for logging (remove sensitive data)
   */
  static sanitizeRequestBody(body: any, path: string): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    
    // Remove passwords and sensitive auth data
    const sensitiveFields = [
      'password', 
      'confirmPassword', 
      'currentPassword', 
      'newPassword',
      'token', 
      'accessToken', 
      'refreshToken',
      'secret',
      'apiKey',
      'privateKey',
      'ssn',
      'socialSecurityNumber',
      'creditCard',
      'cardNumber',
      'cvv',
      'pin'
    ];

    // Recursively sanitize nested objects
    const sanitizeObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        if (sensitiveFields.some(field => lowerKey.includes(field))) {
          result[key] = '[REDACTED]';
        } else {
          result[key] = sanitizeObject(value);
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }

  /**
   * Sanitize response body for logging
   */
  static sanitizeResponseBody(body: any, statusCode: number): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    // Don't log large successful responses (like file downloads)
    if (statusCode >= 200 && statusCode < 300 && typeof body === 'object') {
      const bodyStr = JSON.stringify(body);
      if (bodyStr.length > 50000) { // 50KB limit
        return { 
          message: '[RESPONSE TOO LARGE]',
          size: bodyStr.length,
          type: Array.isArray(body) ? 'array' : 'object'
        };
      }
    }

    // For error responses, keep full details
    return body;
  }

  /**
   * Check if path should be logged
   */
  static shouldLog(path: string, method: string): boolean {
    // Skip logging for certain paths
    const skipPaths = [
      '/health',
      '/doc',
      '/reference',
      '/favicon.ico',
      '/_log' // Don't log requests to the logging endpoints themselves
    ];

    // Skip OPTIONS requests (CORS preflight)
    if (method === 'OPTIONS') {
      return false;
    }

    return !skipPaths.some(skipPath => path.startsWith(skipPath));
  }
} 