import { create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";

const tags = ["Logs"];

// Response schemas
const logItemSchema = z.object({
  id: z.string().uuid(),
  method: z.string(),
  url: z.string(),
  path: z.string(),
  statusCode: z.number().nullable(),
  duration: z.number().nullable(),
  userAgent: z.string().nullable(),
  ipAddress: z.string().nullable(),
  userId: z.string().uuid().nullable(),
  workspaceId: z.string().uuid().nullable(),
  errorMessage: z.string().nullable(),
  createdAt: z.string(),
}).openapi("LogItem");

const logsListSchema = z.object({
  data: z.array(logItemSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
}).openapi("LogsList");

const logDetailSchema = z.object({
  id: z.string().uuid(),
  method: z.string(),
  url: z.string(),
  path: z.string(),
  userAgent: z.string().nullable(),
  ipAddress: z.string().nullable(),
  userId: z.string().uuid().nullable(),
  workspaceId: z.string().uuid().nullable(),
  headers: z.record(z.string()).nullable(),
  queryParams: z.record(z.string()).nullable(),
  requestBody: z.any().nullable(),
  statusCode: z.number().nullable(),
  responseBody: z.any().nullable(),
  responseHeaders: z.record(z.string()).nullable(),
  startTime: z.string(),
  endTime: z.string().nullable(),
  duration: z.number().nullable(),
  errorMessage: z.string().nullable(),
  errorStack: z.string().nullable(),
  createdAt: z.string(),
}).openapi("LogDetail");

const deleteResultSchema = z.object({
  deleted: z.number(),
  message: z.string(),
}).openapi("DeleteResult");

export const getLogsRoute = createRoute({
  path: "/v1/logs",
  method: "get",
  summary: "Get API logs",
  description: "Retrieve API request/response logs with filtering and pagination",
  tags,
  request: {
    query: z.object({
      page: z.coerce.number().min(1).default(1).optional(),
      limit: z.coerce.number().min(1).max(100).default(20).optional(),
      method: z.string().optional(),
      status_code: z.coerce.number().optional(),
      path: z.string().optional(),
      user_id: z.string().uuid().optional(),
      from_date: z.string().datetime().optional(),
      to_date: z.string().datetime().optional(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: logsListSchema,
        },
      },
      description: "API logs list",
    },
    400: {
      description: "Validation error",
    },
    401: {
      description: "Unauthorized",
    },
    500: {
      description: "Internal server error",
    },
  },
});

export const getLogByIdRoute = createRoute({
  path: "/v1/logs/{id}",
  method: "get",
  summary: "Get API log details",
  description: "Retrieve detailed information for a specific API log entry",
  tags,
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: logDetailSchema,
        },
      },
      description: "API log details",
    },
    404: {
      description: "Log not found",
    },
    401: {
      description: "Unauthorized",
    },
    500: {
      description: "Internal server error",
    },
  },
});

export const deleteLogsRoute = createRoute({
  path: "/v1/logs",
  method: "delete",
  summary: "Delete old API logs",
  description: "Delete API logs older than specified date",
  tags,
  request: {
    query: z.object({
      older_than: z.string().datetime().describe("Delete logs older than this date"),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteResultSchema,
        },
      },
      description: "Deletion result",
    },
    400: {
      description: "Validation error",
    },
    401: {
      description: "Unauthorized",
    },
    500: {
      description: "Internal server error",
    },
  },
}); 