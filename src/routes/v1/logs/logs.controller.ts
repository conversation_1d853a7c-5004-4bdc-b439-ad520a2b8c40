import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { LogsService } from "./logs.service";
import type { 
  getLogsRoute, 
  getLogByIdRoute,
  deleteLogsRoute
} from "./logs.routes";

export const getLogs: AppRouteHandler<typeof getLogsRoute> = async (c) => {
  const query = c.req.valid("query");
  const user = c.get("user");
  
  // Only allow admins or owners to view logs
  if (!user || !user.role || !['admin', 'owner'].includes(user.role)) {
    throw new HTTPException(403, { message: "Insufficient permissions" });
  }

  try {
    const result = await LogsService.getLogs(query, user.workspace?.id);
    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }
    throw new HTTPException(500, { message: "Failed to retrieve logs" });
  }
};

export const getLogById: AppRouteHandler<typeof getLogByIdRoute> = async (c) => {
  const { id } = c.req.valid("param");
  const user = c.get("user");
  
  // Only allow admins or owners to view logs
  if (!user || !user.role || !['admin', 'owner'].includes(user.role)) {
    throw new HTTPException(403, { message: "Insufficient permissions" });
  }

  try {
    const log = await LogsService.getLogById(id, user.workspace?.id);
    return c.json(log, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }
    throw new HTTPException(500, { message: "Failed to retrieve log" });
  }
};

export const deleteLogs: AppRouteHandler<typeof deleteLogsRoute> = async (c) => {
  const { older_than } = c.req.valid("query");
  const user = c.get("user");
  
  // Only allow owners to delete logs
  if (!user || !user.role || user.role !== 'owner') {
    throw new HTTPException(403, { message: "Insufficient permissions - only owners can delete logs" });
  }

  try {
    const result = await LogsService.deleteOldLogs(older_than);
    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }
    throw new HTTPException(500, { message: "Failed to delete logs" });
  }
}; 