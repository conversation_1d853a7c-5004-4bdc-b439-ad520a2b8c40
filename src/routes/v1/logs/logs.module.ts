import { createRouter } from "@/lib/create-app";
import { authenticatedEndpoint } from "@/lib/middleware-utils";
import * as routes from "./logs.routes";
import * as handlers from "./logs.controller";

const router = createRouter()
  .openapi(routes.getLogsRoute, handlers.getLogs)
  .openapi(routes.getLogByIdRoute, handlers.getLogById)
  .openapi(routes.deleteLogsRoute, handlers.deleteLogs);

// Apply authenticated endpoint middleware to all routes
export default router.use("*", authenticatedEndpoint()); 