import { createRouter } from "@/lib/create-app";

import * as handlers from "./auth.controller";
import * as routes from "./auth.routes";

const router = createRouter()
  .openapi(routes.signUpRoute, handlers.signUp)
  .openapi(routes.signInRoute, handlers.signIn)
  .openapi(routes.signOutRoute, handlers.signOut)
  .openapi(routes.refreshRoute, handlers.refresh)
  .openapi(routes.forgotPasswordRoute, handlers.forgotPassword)
  .openapi(routes.resetPasswordRoute, handlers.resetPassword)
  .openapi(routes.callbackRoute, handlers.callback);

export default router; 