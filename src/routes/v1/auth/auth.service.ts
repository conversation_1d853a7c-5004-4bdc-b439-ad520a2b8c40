import { SupabaseAuth, supabase } from "@/lib/supabase";
import { HTTPException } from "hono/http-exception";
import db from "@/db";
import { workspaces, userProfiles } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import type { PinoLogger } from "hono-pino";

export interface SignUpData {
  email: string;
  password: string;
  confirmPassword: string;
  first_name: string;
  last_name: string;
  company_name: string;
  company_type: 'individual' | 'team' | 'firm';
  phone?: string;
  license_number?: string;
  website?: string;
  address?: string;
  terms_accepted: boolean;
  marketing_consent?: boolean;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface RefreshTokenData {
  refresh_token: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  new_password: string;
  confirm_password: string;
}

export class AuthService {
  static async signUp(data: SignUpData, logger: PinoLogger) {
    logger.info({ email: data.email }, 'Starting signup process');
    
    // Validate password confirmation
    if (data.password !== data.confirmPassword) {
      logger.error('Password mismatch validation failed');
      const error = new Error("Password and confirm password do not match");
      (error as any).code = "PASSWORD_MISMATCH";
      throw error;
    }

    // Validate terms acceptance
    if (!data.terms_accepted) {
      logger.error('Terms acceptance validation failed');
      const error = new Error("Terms and conditions must be accepted");
      (error as any).code = "TERMS_NOT_ACCEPTED";
      throw error;
    }

    logger.info('Validation passed, creating user in Supabase Auth');

    // Create user in Supabase Auth
    let user, session;
    try {
      const result = await SupabaseAuth.signUp(data.email, data.password);
      user = result.user;
      session = result.session;
      logger.info({
        userId: user?.id,
        email: user?.email,
        hasSession: !!session
      }, 'Supabase Auth user created');
    } catch (error: any) {
      logger.error({ error }, 'Supabase Auth signup failed');
      throw error;
    }
    
    if (!user) {
      logger.error('No user returned from Supabase Auth');
      throw new HTTPException(400, { message: "Failed to create user account" });
    }

    // Note: session might be null if email confirmation is required
    logger.info({ 
      hasSession: !!session,
      userId: user.id,
      emailConfirmed: !!user.email_confirmed_at 
    }, 'User created, checking session status');

    logger.info('Creating workspace');
    const workspaceData = {
      companyName: data.company_name,
      companyType: data.company_type,
      phone: data.phone,
      licenseNumber: data.license_number,
      website: data.website,
      address: data.address,
      status: 'trial',
      subscriptionPlan: 'trial',
      onboardingCompleted: false,
      onboardingStep: 1,
    };
    logger.debug({ workspaceData }, 'Workspace data');

    // Create workspace
    let workspace;
    try {
      const result = await db.insert(workspaces).values(workspaceData).returning();
      workspace = result[0];
      logger.info({
        id: workspace.id,
        companyName: workspace.companyName
      }, 'Workspace created');
    } catch (error: any) {
      logger.error({ error }, 'Workspace creation failed');
      throw new Error(`Workspace creation failed: ${error.message}`);
    }

    logger.info('Creating user profile');
    const profileData = {
      userId: user.id,
      workspaceId: workspace.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      displayName: `${data.first_name} ${data.last_name}`,
      role: 'owner',
      phone: data.phone,
      licenseNumber: data.license_number,
      isActive: true,
      joinedAt: new Date().toISOString(),
    };
    logger.debug({ profileData }, 'Profile data');

    // Create user profile
    let profile;
    try {
      const result = await db.insert(userProfiles).values(profileData).returning();
      profile = result[0];
      logger.info({
        id: profile.id,
        userId: profile.userId,
        workspaceId: profile.workspaceId,
        email: profile.email
      }, 'User profile created');
    } catch (error: any) {
      logger.error({ error }, 'User profile creation failed');
      throw new Error(`User profile creation failed: ${error.message}`);
    }

    return {
      user: {
        id: user.id,
        email: user.email!,
        created_at: user.created_at!,
      },
      workspace: {
        id: workspace.id,
        companyName: workspace.companyName,
        companyType: workspace.companyType!,
        subscriptionPlan: workspace.subscriptionPlan!,
        status: workspace.status!,
        createdAt: workspace.createdAt!,
      },
      profile: {
        id: profile.id,
        workspaceId: profile.workspaceId!,
        role: profile.role!,
        firstName: profile.firstName!,
        lastName: profile.lastName!,
        displayName: profile.displayName!,
        email: profile.email!,
      },
      session: session ? {
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at!,
      } : null,
    };
  }

  static async signIn(data: SignInData) {
    // Authenticate with Supabase
    const { user, session } = await SupabaseAuth.signIn(data.email, data.password);
    
    if (!user || !session) {
      throw new HTTPException(401, { message: "Invalid credentials" });
    }

    // Get user's default/first active workspace
    const profiles = await db
      .select({
        id: userProfiles.id,
        workspaceId: userProfiles.workspaceId,
        role: userProfiles.role,
        firstName: userProfiles.firstName,
        lastName: userProfiles.lastName,
        displayName: userProfiles.displayName,
        email: userProfiles.email,
        isActive: userProfiles.isActive,
        workspace: {
          id: workspaces.id,
          companyName: workspaces.companyName,
          companyType: workspaces.companyType,
          subscriptionPlan: workspaces.subscriptionPlan,
          status: workspaces.status,
        }
      })
      .from(userProfiles)
      .leftJoin(workspaces, eq(userProfiles.workspaceId, workspaces.id))
      .where(and(
        eq(userProfiles.userId, user.id),
        eq(userProfiles.isActive, true)
      ))
      .limit(1);

    if (profiles.length === 0) {
      throw new HTTPException(403, { message: "User does not have access to any workspace" });
    }
    
    const userProfile = profiles[0];

    if (!userProfile.workspace) {
      throw new HTTPException(403, { message: "Associated workspace not found" });
    }

    return {
      user: {
        id: user.id,
        email: user.email!,
        created_at: user.created_at!,
      },
      session: {
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at!,
      },
      workspace: {
        id: userProfile.workspace.id,
        companyName: userProfile.workspace.companyName,
        companyType: userProfile.workspace.companyType!,
        subscriptionPlan: userProfile.workspace.subscriptionPlan!,
        status: userProfile.workspace.status!,
        createdAt: new Date().toISOString(), // This would need to be from the actual workspace
      },
      profile: {
        id: userProfile.id,
        workspaceId: userProfile.workspaceId!,
        role: userProfile.role!,
        firstName: userProfile.firstName || undefined,
        lastName: userProfile.lastName || undefined,
        displayName: userProfile.displayName || undefined,
        email: userProfile.email!,
      },
    };
  }

  static async signOut() {
    await SupabaseAuth.signOut();
    return { success: true };
  }

  static async refreshToken(data: RefreshTokenData) {
    const { data: sessionData, error } = await supabase.auth.refreshSession({
      refresh_token: data.refresh_token
    });

    if (error || !sessionData.session) {
      throw new HTTPException(401, { message: "Invalid or expired refresh token" });
    }

    return {
      access_token: sessionData.session.access_token,
      refresh_token: sessionData.session.refresh_token,
      expires_at: sessionData.session.expires_at!,
    };
  }

  static async forgotPassword(data: ForgotPasswordData) {
    await SupabaseAuth.resetPassword(data.email);
    return {
      success: true,
      message: "Password reset email sent successfully"
    };
  }

  static async resetPassword(data: ResetPasswordData) {
    // Validate password confirmation
    if (data.new_password !== data.confirm_password) {
      throw new HTTPException(400, { message: "New password and confirm password do not match" });
    }

    // Verify and update password using Supabase
    const { error } = await supabase.auth.updateUser({
      password: data.new_password
    });

    if (error) {
      throw new HTTPException(400, { message: "Invalid or expired reset token" });
    }

    return { success: true };
  }

  static async handleCallback(token?: string, type?: string, access_token?: string, refresh_token?: string) {
    // Handle email verification callback
    if (type === 'signup' && token) {
      // Verify the email confirmation token
      const { error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'email'
      });

      if (error) {
        throw new HTTPException(400, { message: "Invalid verification token" });
      }

      return { redirect: '/dashboard' };
    }

    // Handle password reset callback
    if (type === 'recovery' && access_token && refresh_token) {
      // Set the session and redirect to password reset form
      const { error } = await supabase.auth.setSession({
        access_token,
        refresh_token
      });

      if (error) {
        throw new HTTPException(400, { message: "Invalid reset token" });
      }

      return { redirect: '/auth/reset-password' };
    }

    // Default for unknown callback types
    throw new HTTPException(400, { message: "Invalid callback" });
  }
} 