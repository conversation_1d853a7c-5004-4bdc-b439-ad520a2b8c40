import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { createErrorResponse } from "@/middlewares/validation";
import { AuthService } from "./auth.service";
import type { 
  signUpRoute, 
  signInRoute, 
  signOutRoute,
  refreshRoute,
  forgotPasswordRoute,
  resetPasswordRoute,
  callbackRoute
} from "./auth.routes";

export const signUp: AppRouteHandler<typeof signUpRoute> = async (c) => {
  const data = c.req.valid("json");
  const logger = c.var.logger;
  
  logger.info({
    email: data.email,
    company_name: data.company_name,
    company_type: data.company_type,
    terms_accepted: data.terms_accepted
  }, 'Signup controller received request');
  
  try {
    const result = await AuthService.signUp(data, logger);
    logger.info({ email: data.email }, 'Signup successful');
    return c.json(result, 201);
  } catch (error: any) {
    logger.error({
      message: error.message,
      code: error.code,
      stack: error.stack,
      email: data.email
    }, 'Signup controller error');
    if (error.code === "PASSWORD_MISMATCH") {
      return c.json(createErrorResponse(
        'PASSWORD_MISMATCH',
        'Password and confirm password do not match',
        c.req.path
      ), 400);
    }
    if (error.code === "TERMS_NOT_ACCEPTED") {
      return c.json(createErrorResponse(
        'TERMS_NOT_ACCEPTED',
        'Terms and conditions must be accepted',
        c.req.path
      ), 400);
    }
    if (error.message?.includes("already registered")) {
      return c.json(createErrorResponse(
        'USER_EXISTS',
        'User with this email already exists',
        c.req.path
      ), 409);
    }
    if (error instanceof HTTPException) {
      throw error;
    }
    return c.json(createErrorResponse(
      'SIGNUP_FAILED',
      error.message || 'Database error saving new user',
      c.req.path
    ), 400);
  }
};

export const signIn: AppRouteHandler<typeof signInRoute> = async (c) => {
  const data = c.req.valid("json");

  try {
    const result = await AuthService.signIn(data);
    return c.json(result);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.status === 403) {
        if (error.message.includes("User does not have access to any workspace")) {
          return c.json(createErrorResponse(
            'NO_WORKSPACE_ACCESS',
            'User does not have access to any workspace',
            c.req.path
          ), 403);
        }
        if (error.message.includes("Associated workspace not found")) {
          return c.json(createErrorResponse(
            'WORKSPACE_NOT_FOUND',
            'Associated workspace not found',
            c.req.path
          ), 403);
        }
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'SIGNIN_FAILED',
      'Invalid credentials',
      c.req.path
    ), 401);
  }
};

export const signOut: AppRouteHandler<typeof signOutRoute> = async (c) => {
  c.req.valid("json"); // Validate the request body

  try {
    const result = await AuthService.signOut();
    return c.json(result);
  } catch (error: any) {
    return c.json(createErrorResponse(
      'SIGNOUT_FAILED',
      'Failed to sign out user',
      c.req.path
    ), 400);
  }
};

export const refresh: AppRouteHandler<typeof refreshRoute> = async (c) => {
  const data = c.req.valid("json");

  try {
    const result = await AuthService.refreshToken(data);
    return c.json(result);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.status === 401) {
        return c.json(createErrorResponse(
          'INVALID_REFRESH_TOKEN',
          'Invalid or expired refresh token',
          c.req.path
        ), 401);
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'REFRESH_FAILED',
      'Failed to refresh authentication token',
      c.req.path
    ), 401);
  }
};

export const forgotPassword: AppRouteHandler<typeof forgotPasswordRoute> = async (c) => {
  const data = c.req.valid("json");

  try {
    const result = await AuthService.forgotPassword(data);
    return c.json(result);
  } catch (error: any) {
    return c.json(createErrorResponse(
      'FORGOT_PASSWORD_FAILED',
      error.message || 'Failed to send password reset email',
      c.req.path
    ), 400);
  }
};

export const resetPassword: AppRouteHandler<typeof resetPasswordRoute> = async (c) => {
  const data = c.req.valid("json");

  try {
    const result = await AuthService.resetPassword(data);
    return c.json(result);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.message.includes("New password and confirm password do not match")) {
        return c.json(createErrorResponse(
          'PASSWORD_MISMATCH',
          'New password and confirm password do not match',
          c.req.path
        ), 400);
      }
      if (error.message.includes("Invalid or expired reset token")) {
        return c.json(createErrorResponse(
          'INVALID_RESET_TOKEN',
          'Invalid or expired reset token',
          c.req.path
        ), 400);
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'RESET_PASSWORD_FAILED',
      'Failed to reset password',
      c.req.path
    ), 400);
  }
};

export const callback: AppRouteHandler<typeof callbackRoute> = async (c) => {
  const { token, type, access_token, refresh_token } = c.req.valid("query");

  try {
    const result = await AuthService.handleCallback(token, type, access_token, refresh_token);
    return c.redirect(result.redirect);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      return c.redirect(`/auth/error?message=${encodeURIComponent(error.message)}`);
    }
    return c.redirect('/auth/error?message=Callback processing failed');
  }
}; 