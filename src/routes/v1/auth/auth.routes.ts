import { create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";

// Request schemas - registered as OpenAPI components
const signUpRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  confirmPassword: z.string().min(6),
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  company_name: z.string().min(1),
  company_type: z.enum(['individual', 'team', 'firm']),
  phone: z.string().optional(),
  license_number: z.string().optional(),
  website: z.string().url().optional(),
  address: z.string().optional(),
  terms_accepted: z.boolean(),
  marketing_consent: z.boolean().optional(),
}).openapi("SignUpRequest");

const signInRequestSchema = z.object({
  email: z.string().email().describe("User's email address"),
  password: z.string().min(6).describe("User's password (minimum 6 characters)"),
}).openapi("SignInRequest", {
  example: {
    email: "<EMAIL>",
    password: "securePassword123"
  }
});

const signOutRequestSchema = z.object({
  refresh_token: z.string(),
}).openapi("SignOutRequest");

const refreshRequestSchema = z.object({
  refresh_token: z.string(),
}).openapi("RefreshRequest");

const forgotPasswordRequestSchema = z.object({
  email: z.string().email(),
}).openapi("ForgotPasswordRequest");

const resetPasswordRequestSchema = z.object({
  token: z.string(),
  new_password: z.string().min(6),
  confirm_password: z.string().min(6),
}).openapi("ResetPasswordRequest");

// Response schemas - registered as OpenAPI components
const userSchema = z.object({
  id: z.string().describe("Unique user identifier from Supabase Auth"),
  email: z.string().describe("User's email address"),
  created_at: z.string().describe("ISO timestamp when the user was created"),
}).openapi("User");

const workspaceSchema = z.object({
  id: z.string().describe("Unique workspace identifier"),
  companyName: z.string().describe("Company or organization name"),
  companyType: z.enum(['individual', 'team', 'firm']).describe("Type of organization"),
  subscriptionPlan: z.string().describe("Current subscription plan (e.g., 'trial', 'basic', 'pro')"),
  status: z.string().describe("Workspace status (e.g., 'active', 'trial', 'suspended')"),
  createdAt: z.string().describe("ISO timestamp when the workspace was created"),
}).openapi("Workspace");

const userProfileSchema = z.object({
  id: z.string().describe("Unique profile identifier"),
  workspaceId: z.string().describe("Associated workspace ID"),
  role: z.string().describe("User's role in the workspace (e.g., 'owner', 'admin', 'member')"),
  firstName: z.string().optional().describe("User's first name"),
  lastName: z.string().optional().describe("User's last name"),
  displayName: z.string().optional().describe("User's display name"),
  email: z.string().describe("User's email address"),
}).openapi("UserProfile");

const authSessionSchema = z.object({
  access_token: z.string().describe("JWT access token for API authentication"),
  refresh_token: z.string().describe("Refresh token for obtaining new access tokens"),
  expires_at: z.number().describe("Unix timestamp when the access token expires"),
}).openapi("AuthSession");

const signUpResponseSchema = z.object({
  user: userSchema,
  workspace: workspaceSchema,
  profile: userProfileSchema,
  session: authSessionSchema,
}).openapi("SignUpResponse");

const signInResponseSchema = z.object({
  user: userSchema.describe("Authenticated user information"),
  session: authSessionSchema.describe("Authentication session tokens"),
  workspace: workspaceSchema.describe("User's default workspace information"),
  profile: userProfileSchema.describe("User's profile within the workspace"),
}).openapi("SignInResponse", {
  example: {
    user: {
      id: "123e4567-e89b-12d3-a456-426614174000",
      email: "<EMAIL>",
      created_at: "2024-01-15T10:30:00.000Z"
    },
    session: {
      access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      refresh_token: "def50200123456789abcdef...",
      expires_at: 1705312200
    },
    workspace: {
      id: "456e7890-e89b-12d3-a456-426614174001",
      companyName: "Acme Real Estate",
      companyType: "firm",
      subscriptionPlan: "pro",
      status: "active",
      createdAt: "2024-01-15T10:30:00.000Z"
    },
    profile: {
      id: "789e0123-e89b-12d3-a456-426614174002",
      workspaceId: "456e7890-e89b-12d3-a456-426614174001",
      role: "owner",
      firstName: "John",
      lastName: "Doe",
      displayName: "John Doe",
      email: "<EMAIL>"
    }
  }
});

const signOutResponseSchema = z.object({
  success: z.boolean(),
}).openapi("SignOutResponse");

const refreshResponseSchema = z.object({
  access_token: z.string(),
  refresh_token: z.string(),
  expires_at: z.number(),
}).openapi("RefreshResponse");

const forgotPasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
}).openapi("ForgotPasswordResponse");

const resetPasswordResponseSchema = z.object({
  success: z.boolean(),
}).openapi("ResetPasswordResponse");

// Routes
export const signUpRoute = createRoute({
  method: "post",
  path: "/v1/auth/signup",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signUpRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: signUpResponseSchema,
        },
      },
      description: "User registration with workspace creation",
    },
    400: {
      description: "Bad request - validation errors",
    },
    409: {
      description: "User already exists",
    },
  },
  tags: ["Authentication"],
});

export const signInRoute = createRoute({
  method: "post",
  path: "/v1/auth/signin",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signInRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: signInResponseSchema,
        },
      },
      description: "Successful authentication. Returns user information, authentication tokens, default workspace details, and user profile within that workspace.",
    },
    401: {
      description: "Authentication failed - invalid email or password",
    },
    403: {
      description: "User account exists but has no access to any workspace, or associated workspace not found",
    },
  },
  tags: ["Authentication"],
});

export const signOutRoute = createRoute({
  method: "post",
  path: "/v1/auth/signout",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signOutRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: signOutResponseSchema,
        },
      },
      description: "User logout",
    },
    400: {
      description: "Bad request",
    },
  },
  tags: ["Authentication"],
});

export const refreshRoute = createRoute({
  method: "post",
  path: "/v1/auth/refresh",
  request: {
    body: {
      content: {
        "application/json": {
          schema: refreshRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: refreshResponseSchema,
        },
      },
      description: "Refresh authentication token",
    },
    401: {
      description: "Invalid refresh token",
    },
  },
  tags: ["Authentication"],
});

export const forgotPasswordRoute = createRoute({
  method: "post",
  path: "/v1/auth/forgot-password",
  request: {
    body: {
      content: {
        "application/json": {
          schema: forgotPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: forgotPasswordResponseSchema,
        },
      },
      description: "Password reset request",
    },
    400: {
      description: "Bad request",
    },
  },
  tags: ["Authentication"],
});

export const resetPasswordRoute = createRoute({
  method: "post",
  path: "/v1/auth/reset-password",
  request: {
    body: {
      content: {
        "application/json": {
          schema: resetPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: resetPasswordResponseSchema,
        },
      },
      description: "Password reset confirmation",
    },
    400: {
      description: "Bad request - invalid token or passwords don't match",
    },
  },
  tags: ["Authentication"],
});

export const callbackRoute = createRoute({
  method: "get",
  path: "/v1/auth/callback",
  request: {
    query: z.object({
      token: z.string().optional(),
      type: z.string().optional(),
      access_token: z.string().optional(),
      refresh_token: z.string().optional(),
    }),
  },
  responses: {
    302: {
      description: "Redirect to dashboard or error page",
    },
    400: {
      description: "Invalid callback parameters",
    },
  },
  tags: ["Authentication"],
}); 