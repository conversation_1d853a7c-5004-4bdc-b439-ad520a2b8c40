import { create<PERSON>oute, z } from "@hono/zod-openapi";

// Request schemas - registered as OpenAPI components
const uploadFileRequestSchema = z.object({
  file_type: z.enum(['document', 'image', 'video', 'audio', 'other']),
  entity_type: z.string().optional(),
  entity_id: z.string().uuid().optional(),
  is_public: z.boolean().optional().default(false),
}).openapi("UploadFileRequest");

// Response schemas - registered as OpenAPI components
const fileSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  uploaded_by: z.string().uuid(),
  file_name: z.string(),
  original_name: z.string(),
  mime_type: z.string(),
  file_size: z.number(),
  storage_path: z.string(),
  storage_url: z.string().optional(),
  file_type: z.enum(['document', 'image', 'video', 'audio', 'other']),
  entity_type: z.string().optional(),
  entity_id: z.string().uuid().optional(),
  is_public: z.boolean(),
  metadata: z.record(z.any()),
  created_at: z.string(),
  updated_at: z.string(),
}).openapi("File");

const processingStatusSchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  progress: z.number().min(0).max(100).optional(),
  estimated_completion: z.string().optional(),
}).openapi("ProcessingStatus");

const uploadFileResponseSchema = z.object({
  success: z.boolean(),
  file: fileSchema,
  processing: processingStatusSchema.optional(),
}).openapi("UploadFileResponse");

const getFileResponseSchema = fileSchema.extend({
  signed_url: z.string().optional(),
  can_download: z.boolean(),
  can_delete: z.boolean(),
  can_update: z.boolean(),
}).openapi("GetFileResponse");

const deleteFileResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  deleted_file: z.object({
    id: z.string().uuid(),
    file_name: z.string(),
    storage_path: z.string(),
  }),
}).openapi("DeleteFileResponse");

// Routes
export const uploadFileRoute = createRoute({
  method: "post",
  path: "/v1/files/upload",
  request: {
    body: {
      content: {
        "multipart/form-data": {
          schema: uploadFileRequestSchema.extend({
            file: z.any().describe("The file to upload"),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: uploadFileResponseSchema,
        },
      },
      description: "File uploaded successfully",
    },
    400: {
      description: "Bad request - validation errors or file processing failed",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    413: {
      description: "Payload too large - file size exceeds limit",
    },
  },
  tags: ["Files"],
});

export const getFileRoute = createRoute({
  method: "get",
  path: "/v1/files/{file_id}",
  request: {
    params: z.object({
      file_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: getFileResponseSchema,
        },
      },
      description: "File metadata and download URL",
    },
    404: {
      description: "File not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Files"],
});

export const deleteFileRoute = createRoute({
  method: "delete",
  path: "/v1/files/{file_id}",
  request: {
    params: z.object({
      file_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteFileResponseSchema,
        },
      },
      description: "File deleted successfully",
    },
    404: {
      description: "File not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Files"],
});