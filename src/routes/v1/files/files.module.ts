import { createRouter } from "@/lib/create-app";
import { authenticatedEndpoint } from "@/lib/middleware-utils";
import * as routes from "./files.routes";
import * as handlers from "./files.controller";

const router = createRouter()
  .openapi(routes.uploadFileRoute, handlers.uploadFile)
  .openapi(routes.getFileRoute, handlers.getFile)
  .openapi(routes.deleteFileRoute, handlers.deleteFile);

// Apply authenticated endpoint middleware to all routes
export default router.use("*", authenticatedEndpoint());