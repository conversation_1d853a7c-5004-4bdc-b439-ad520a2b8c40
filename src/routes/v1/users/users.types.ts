import { z } from "@hono/zod-openapi";

// User Role enum
export const UserRoleSchema = z.enum([
  "owner",
  "admin", 
  "manager",
  "member",
  "viewer"
]).openapi("UserRole");

export type UserRole = z.infer<typeof UserRoleSchema>;

// User Preferences schemas
export const NotificationPreferencesSchema = z.object({
  email_notifications: z.boolean(),
  push_notifications: z.boolean(),
  listing_updates: z.boolean(),
  team_updates: z.boolean(),
  system_updates: z.boolean(),
}).openapi("NotificationPreferences");

export const DisplayPreferencesSchema = z.object({
  timezone: z.string(),
  date_format: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
  currency: z.string(),
  language: z.string(),
}).openapi("DisplayPreferences");

export const PrivacyPreferencesSchema = z.object({
  profile_visibility: z.enum(['public', 'team', 'private']),
  contact_visibility: z.enum(['public', 'team', 'private']),
}).openapi("PrivacyPreferences");

export const UserPreferencesSchema = z.object({
  notifications: NotificationPreferencesSchema,
  display: DisplayPreferencesSchema,
  privacy: PrivacyPreferencesSchema,
}).openapi("UserPreferences");

export type UserPreferences = z.infer<typeof UserPreferencesSchema>;

// User Profile Response schema
export const UserProfileResponseSchema = z.object({
  id: z.string(),
  workspace_id: z.string(),
  email: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  role: UserRoleSchema,
  phone: z.string().optional(),
  license_number: z.string().optional(),
  bio: z.string().optional(),
  avatar_url: z.string().optional(),
  specialties: z.array(z.string()),
  is_active: z.boolean(),
  invited_at: z.string().optional(),
  joined_at: z.string().optional(),
  invited_by: z.string().optional(),
  preferences: UserPreferencesSchema,
  last_login_at: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
}).openapi("UserProfileResponse");

// Update Profile Request schema
export const UpdateProfileRequestSchema = z.object({
  // Basic Information
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  phone: z.string().optional(),
  license_number: z.string().optional(),
  bio: z.string().optional(),
  specialties: z.array(z.string()).optional(),
  avatar_file_id: z.string().optional(),
  
  // Security Updates
  current_password: z.string().optional(),
  new_password: z.string().optional(),
  confirm_password: z.string().optional(),
  new_email: z.string().email().optional(),
  
  // Preferences
  preferences: z.object({
    notifications: NotificationPreferencesSchema.partial().optional(),
    display: DisplayPreferencesSchema.partial().optional(),
    privacy: PrivacyPreferencesSchema.partial().optional(),
  }).optional(),
}).openapi("UpdateProfileRequest");

// Update Profile Response schema
export const UpdateProfileResponseSchema = z.object({
  id: z.string(),
  workspace_id: z.string(),
  email: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  role: UserRoleSchema,
  phone: z.string().optional(),
  license_number: z.string().optional(),
  bio: z.string().optional(),
  avatar_url: z.string().optional(),
  specialties: z.array(z.string()),
  is_active: z.boolean(),
  preferences: UserPreferencesSchema,
  updated_at: z.string(),
  
  // Response metadata for operations
  operations_completed: z.object({
    profile_updated: z.boolean(),
    password_changed: z.boolean().optional(),
    email_change_initiated: z.boolean().optional(),
    email_verification_sent: z.boolean().optional(),
    avatar_updated: z.boolean().optional(),
  }),
  
  // Warnings or next steps
  requires_reauth: z.boolean().optional(),
  warnings: z.array(z.string()).optional(),
}).openapi("UpdateProfileResponse");

// Email Verification Request schema
export const VerifyEmailRequestSchema = z.object({
  token: z.string(),
  email: z.string().email(),
}).openapi("VerifyEmailRequest");

// Email Verification Response schema
export const VerifyEmailResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  email_updated: z.boolean(),
}).openapi("VerifyEmailResponse");