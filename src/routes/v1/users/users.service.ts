import { HTTPException } from "hono/http-exception";
import db from "@/db";
import { userProfiles, files } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { supabase } from "@/lib/supabase";
import type { UserPreferences } from "./users.types";

export interface UpdateProfileData {
  // Basic Information
  first_name?: string;
  last_name?: string;
  phone?: string;
  license_number?: string;
  bio?: string;
  specialties?: string[];
  avatar_file_id?: string;

  // Security Updates
  current_password?: string;
  new_password?: string;
  confirm_password?: string;
  new_email?: string;

  // Preferences
  preferences?: {
    notifications?: Partial<UserPreferences["notifications"]>;
    display?: Partial<UserPreferences["display"]>;
    privacy?: Partial<UserPreferences["privacy"]>;
  };
}

export interface OperationsCompleted {
  profile_updated: boolean;
  password_changed?: boolean;
  email_change_initiated?: boolean;
  email_verification_sent?: boolean;
  avatar_updated?: boolean;
}

export class UsersService {
  static async getUserProfile(userId: string, workspaceId: string) {
    // Get user profile with avatar URL if available
    const [profile] = await db
      .select({
        id: userProfiles.id,
        workspace_id: userProfiles.workspaceId,
        email: userProfiles.email,
        first_name: userProfiles.firstName,
        last_name: userProfiles.lastName,
        role: userProfiles.role,
        phone: userProfiles.phone,
        license_number: userProfiles.licenseNumber,
        bio: userProfiles.bio,
        avatar_url: userProfiles.avatarUrl,
        specialties: userProfiles.specialties,
        is_active: userProfiles.isActive,
        invited_at: userProfiles.invitedAt,
        joined_at: userProfiles.joinedAt,
        invited_by: userProfiles.invitedBy,
        preferences: userProfiles.preferences,
        last_login_at: userProfiles.lastLoginAt,
        created_at: userProfiles.createdAt,
        updated_at: userProfiles.updatedAt,
      })
      .from(userProfiles)
      .where(
        and(
          eq(userProfiles.userId, userId),
          eq(userProfiles.workspaceId, workspaceId),
          eq(userProfiles.isActive, true)
        )
      )
      .limit(1);

    if (!profile) {
      throw new HTTPException(404, { message: "User profile not found" });
    }

    return {
      id: profile.id,
      workspace_id: profile.workspace_id!,
      email: profile.email!,
      first_name: profile.first_name!,
      last_name: profile.last_name!,
      role: profile.role! as
        | "owner"
        | "admin"
        | "manager"
        | "member"
        | "viewer",
      phone: profile.phone || undefined,
      license_number: profile.license_number || undefined,
      bio: profile.bio || undefined,
      avatar_url: profile.avatar_url || undefined,
      specialties: profile.specialties || [],
      is_active: profile.is_active,
      invited_at: profile.invited_at || undefined,
      joined_at: profile.joined_at || undefined,
      invited_by: profile.invited_by || undefined,
      preferences: profile.preferences as UserPreferences,
      last_login_at: profile.last_login_at || undefined,
      created_at: profile.created_at,
      updated_at: profile.updated_at,
    };
  }

  static async updateUserProfile(
    userId: string,
    workspaceId: string,
    updateData: UpdateProfileData
  ) {
    const operations: OperationsCompleted = {
      profile_updated: false,
    };
    const warnings: string[] = [];
    let requiresReauth = false;

    // Get current profile
    const currentProfile = await this.getUserProfile(userId, workspaceId);

    // Handle avatar file update
    let avatarUrl = currentProfile.avatar_url;
    if (updateData.avatar_file_id) {
      avatarUrl = await this.handleAvatarUpdate(
        updateData.avatar_file_id,
        workspaceId,
        userId
      );
      operations.avatar_updated = true;
    }

    // Handle password change
    if (updateData.new_password) {
      if (!updateData.current_password) {
        throw new HTTPException(400, {
          message: "Current password is required to change password",
        });
      }
      if (updateData.new_password !== updateData.confirm_password) {
        throw new HTTPException(400, {
          message: "New password and confirmation do not match",
        });
      }

      await this.changePassword(
        userId,
        updateData.current_password,
        updateData.new_password
      );
      operations.password_changed = true;
      requiresReauth = true;
    }

    // Handle email change
    if (updateData.new_email && updateData.new_email !== currentProfile.email) {
      if (!updateData.current_password) {
        throw new HTTPException(400, {
          message: "Current password is required to change email",
        });
      }

      await this.initiateEmailChange(
        userId,
        updateData.new_email,
        updateData.current_password
      );
      operations.email_change_initiated = true;
      operations.email_verification_sent = true;
      warnings.push(
        "Email change initiated. Please check your new email for verification."
      );
    }

    // Merge preferences
    let updatedPreferences = currentProfile.preferences;
    if (updateData.preferences) {
      updatedPreferences = {
        notifications: {
          ...currentProfile.preferences.notifications,
          ...updateData.preferences.notifications,
        },
        display: {
          ...currentProfile.preferences.display,
          ...updateData.preferences.display,
        },
        privacy: {
          ...currentProfile.preferences.privacy,
          ...updateData.preferences.privacy,
        },
      };
    }

    // Update profile in database
    const updateFields: any = {
      updatedAt: new Date().toISOString(),
    };

    if (updateData.first_name !== undefined)
      updateFields.firstName = updateData.first_name;
    if (updateData.last_name !== undefined)
      updateFields.lastName = updateData.last_name;
    if (updateData.phone !== undefined) updateFields.phone = updateData.phone;
    if (updateData.license_number !== undefined)
      updateFields.licenseNumber = updateData.license_number;
    if (updateData.bio !== undefined) updateFields.bio = updateData.bio;
    if (updateData.specialties !== undefined)
      updateFields.specialties = updateData.specialties;
    if (avatarUrl !== currentProfile.avatar_url)
      updateFields.avatarUrl = avatarUrl;
    if (updateData.preferences) updateFields.preferences = updatedPreferences;

    if (Object.keys(updateFields).length > 1) {
      // More than just updatedAt
      await db
        .update(userProfiles)
        .set(updateFields)
        .where(
          and(
            eq(userProfiles.userId, userId),
            eq(userProfiles.workspaceId, workspaceId)
          )
        );

      operations.profile_updated = true;
    }

    // Get updated profile
    const updatedProfile = await this.getUserProfile(userId, workspaceId);

    return {
      ...updatedProfile,
      operations_completed: operations,
      requires_reauth: requiresReauth || undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  private static async handleAvatarUpdate(
    fileId: string,
    workspaceId: string,
    userId: string
  ): Promise<string | undefined> {
    // Verify the file exists and belongs to the user
    const [file] = await db
      .select()
      .from(files)
      .where(
        and(
          eq(files.id, fileId),
          eq(files.workspaceId, workspaceId),
          eq(files.uploadedBy, userId),
          eq(files.fileType, "avatar")
        )
      )
      .limit(1);

    if (!file) {
      throw new HTTPException(400, {
        message: "Avatar file not found or access denied",
      });
    }

    // Return the storage URL or generate a signed URL for private files
    if (file.isPublic && file.storageUrl) {
      return file.storageUrl;
    } else {
      // Generate a long-lived signed URL for avatar (24 hours)
      const { data, error } = await supabase.storage
        .from("files")
        .createSignedUrl(file.storagePath, 86400); // 24 hours

      if (error) {
        throw new HTTPException(500, {
          message: "Failed to generate avatar URL",
        });
      }

      return data.signedUrl;
    }
  }

  private static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ) {
    // First verify current password by attempting to sign in
    const { error: verifyError } = await supabase.auth.signInWithPassword({
      email: "", // We'll need to get the user's email first
      password: currentPassword,
    });

    // Update password using Supabase Auth
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      password: newPassword,
    });

    if (error) {
      throw new HTTPException(400, {
        message: `Password change failed: ${error.message}`,
      });
    }
  }

  private static async initiateEmailChange(
    userId: string,
    newEmail: string,
    currentPassword: string
  ) {
    // First verify current password by getting user and attempting verification
    const { data: user, error: getUserError } =
      await supabase.auth.admin.getUserById(userId);

    if (getUserError || !user) {
      throw new HTTPException(400, { message: "User not found" });
    }

    // Verify current password
    const { error: verifyError } = await supabase.auth.signInWithPassword({
      email: user.user.email!,
      password: currentPassword,
    });

    if (verifyError) {
      throw new HTTPException(400, {
        message: "Current password is incorrect",
      });
    }

    // Initiate email change using Supabase Auth
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      email: newEmail,
      email_confirm: false, // Require email confirmation
    });

    if (error) {
      throw new HTTPException(400, {
        message: `Email change failed: ${error.message}`,
      });
    }
  }

  static async verifyEmailChange(token: string, email: string) {
    try {
      // Verify the email change token using Supabase
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: "email_change",
      });

      if (error) {
        throw new HTTPException(400, {
          message: `Email verification failed: ${error.message}`,
        });
      }

      // Update the user profile with the new email
      if (data.user) {
        await db
          .update(userProfiles)
          .set({
            email: email,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(userProfiles.userId, data.user.id));
      }

      return {
        success: true,
        message: "Email successfully updated",
        email_updated: true,
      };
    } catch (error) {
      return {
        success: false,
        message: "Email verification failed",
        email_updated: false,
      };
    }
  }
}
