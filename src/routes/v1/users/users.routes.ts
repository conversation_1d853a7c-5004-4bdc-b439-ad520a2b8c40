import { createRoute } from "@hono/zod-openapi";
import type { Context } from "hono";

import { createRouter } from "@/lib/create-app";
import { authMiddleware } from "@/middlewares/auth";
import { validateBody } from "@/middlewares/validation";
import { getAuthenticatedUser, getUserWorkspace } from "@/lib/auth-utils";
import { UsersService } from "./users.service";
import {
  UserProfileResponseSchema,
  UpdateProfileRequestSchema,
  UpdateProfileResponseSchema,
  VerifyEmailRequestSchema,
  VerifyEmailResponseSchema,
} from "./users.types";

// GET /users/profile route
const getUserProfileRoute = createRoute({
  tags: ["Users"],
  method: "get",
  path: "/profile",
  summary: "Get current user profile",
  description: "Get current user profile with workspace context",
  middleware: [authMiddleware],
  responses: {
    200: {
      content: {
        "application/json": {
          schema: UserProfileResponseSchema,
        },
      },
      description: "User profile retrieved successfully",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "User profile not found",
    },
  },
});

// PUT /users/profile route
const updateUserProfileRoute = createRoute({
  tags: ["Users"],
  method: "put",
  path: "/profile",
  summary: "Update user profile",
  description: "Update user profile information (centralized endpoint for all profile updates)",
  middleware: [authMiddleware],
  request: {
    body: {
      content: {
        "application/json": {
          schema: UpdateProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: UpdateProfileResponseSchema,
        },
      },
      description: "Profile updated successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "User profile not found",
    },
  },
});

// POST /users/verify-email route
const verifyEmailRoute = createRoute({
  tags: ["Users"],
  method: "post",
  path: "/verify-email",
  summary: "Verify new email address",
  description: "Verify new email address (called after email change via PUT /users/profile)",
  request: {
    body: {
      content: {
        "application/json": {
          schema: VerifyEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: VerifyEmailResponseSchema,
        },
      },
      description: "Email verification result",
    },
    400: {
      description: "Invalid verification token or email",
    },
  },
});

const router = createRouter()
  .openapi(getUserProfileRoute, async (c) => {
    const user = getAuthenticatedUser(c);
    const workspace = getUserWorkspace(c);

    const profile = await UsersService.getUserProfile(user.id, workspace.id);

    return c.json(profile, 200);
  })
  .openapi(updateUserProfileRoute, async (c) => {
    const user = getAuthenticatedUser(c);
    const workspace = getUserWorkspace(c);
    
    // Validate the request body manually
    const body = await c.req.json();
    const result = UpdateProfileRequestSchema.safeParse(body);
    
    if (!result.success) {
      return c.json({ error: "Validation failed", details: result.error.errors }, 400);
    }
    
    const updateData = result.data;

    const updatedProfile = await UsersService.updateUserProfile(
      user.id,
      workspace.id,
      updateData
    );

    return c.json(updatedProfile, 200);
  })
  .openapi(verifyEmailRoute, async (c) => {
    const body = await c.req.json();
    const { token, email } = body;

    const result = await UsersService.verifyEmailChange(token, email);

    return c.json(result, 200);
  });

export default router;