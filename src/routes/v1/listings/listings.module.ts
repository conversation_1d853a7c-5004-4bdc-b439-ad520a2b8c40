import { createRouter } from "@/lib/create-app";
import { authenticatedEndpoint } from "@/lib/middleware-utils";
import * as routes from "./listings.routes";
import * as handlers from "./listings.controller";

const router = createRouter()
  .openapi(routes.listListingsRoute, handlers.listListings)
  .openapi(routes.getListingRoute, handlers.getListing)
  .openapi(routes.createListingRoute, handlers.createListing)
  .openapi(routes.updateListingRoute, handlers.updateListing)
  .openapi(routes.deleteListingRoute, handlers.deleteListing)
  .openapi(routes.bulkCreateListingsRoute, handlers.bulkCreateListings)
  .openapi(routes.updateListingStatusRoute, handlers.updateListingStatus)
  .openapi(routes.getListingStatusHistoryRoute, handlers.getListingStatusHistory);

// Apply authenticated endpoint middleware to all routes
export default router.use("*", authenticatedEndpoint()); 