import { HTTPException } from "hono/http-exception";
import { eq, and, sql, desc, asc, like, or, count } from "drizzle-orm";
import db from "@/db";
import { listings, listingDetails, listingStatusHistory, userProfiles } from "@/db/schema";

export interface CreateListingData {
  // Core business fields
  business_name: string;
  industry: string;
  asking_price?: number;
  cash_flow_sde?: number;
  annual_revenue?: number;
  status?: string;
  general_location?: string;
  year_established?: number;
  employees?: number;
  owner_hours_week?: number;
  date_listed?: string;
  
  // Assignment
  assigned_to?: string;
  
  // Details
  details?: ListingDetailsData;
  
  // Legacy fields
  title?: string;
  description?: string;
  team_visibility?: string;
  
  // Metadata
  workspace_id: string;
  created_by: string;
}

export interface ListingDetailsData {
  business_description?: string;
  brief_description?: string;
  financial_details?: {
    revenue_2023?: number;
    ebitda?: number;
    assets_included?: string[];
    inventory_value?: number;
    additional_financial_info?: Record<string, any>;
  };
  operations?: {
    business_model?: string;
    key_features?: string[];
    competitive_advantages?: string[];
    operational_details?: Record<string, any>;
  };
  growth_opportunities?: string[];
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available?: boolean;
  equipment_highlights?: string[];
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details?: {
    lease_terms?: string;
    monthly_rent?: number;
    lease_expiration?: string;
    renewal_options?: string;
    landlord_info?: Record<string, any>;
  };
}

export interface UpdateListingData extends Partial<CreateListingData> {}

export interface ListingFilters {
  page?: number;
  limit?: number;
  status?: string;
  industry?: string;
  assigned_to?: string;
  min_price?: number;
  max_price?: number;
  location?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

export interface StatusChangeData {
  status: string;
  reason?: string;
  notes?: string;
  changed_by: string;
  workspace_id: string;
}

export class ListingsService {
  
  /**
   * Calculate days listed for a listing based on date_listed
   */
  private static calculateDaysListed(dateListed?: string | null): number | null {
    if (!dateListed) return null;
    
    const listedDate = new Date(dateListed);
    const today = new Date();
    const diffTime = today.getTime() - listedDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays >= 0 ? diffDays : 0;
  }

  /**
   * Auto-update days_listed field for all active listings
   */
  static async updateDaysListedBatch() {
    const activeListings = await db
      .select({ id: listings.id, dateListed: listings.dateListed })
      .from(listings)
      .where(
        and(
          eq(listings.status, 'active'),
          sql`${listings.dateListed} IS NOT NULL`
        )
      );

    for (const listing of activeListings) {
      const daysListed = this.calculateDaysListed(listing.dateListed);
      if (daysListed !== null) {
        await db
          .update(listings)
          .set({ 
            daysListed,
            updatedAt: sql`now()` 
          })
          .where(eq(listings.id, listing.id));
      }
    }
  }

  /**
   * Get all listings with filtering, sorting and pagination
   */
  static async getListings(filters: ListingFilters, workspaceId: string) {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      industry, 
      assigned_to, 
      min_price, 
      max_price, 
      location, 
      sort_by = 'created_at', 
      sort_order = 'desc', 
      search 
    } = filters;

    // Build where conditions
    const conditions = [eq(listings.workspaceId, workspaceId)];

    if (status) {
      conditions.push(eq(listings.status, status));
    }

    if (industry) {
      conditions.push(eq(listings.industry, industry));
    }

    if (assigned_to) {
      conditions.push(eq(listings.assignedTo, assigned_to));
    }

    if (min_price !== undefined && max_price !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric BETWEEN ${min_price} AND ${max_price}`);
    } else if (min_price !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric >= ${min_price}`);
    } else if (max_price !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric <= ${max_price}`);
    }

    if (location) {
      conditions.push(like(listings.generalLocation, `%${location}%`));
    }

    if (search) {
      conditions.push(
        or(
          like(listings.businessName, `%${search}%`),
          like(listings.industry, `%${search}%`),
          like(listings.description, `%${search}%`),
          like(listings.generalLocation, `%${search}%`)
        )!
      );
    }

    // Apply sorting
    const sortColumn = sort_by === 'asking_price' ? sql`${listings.askingPrice}::numeric` :
                      sort_by === 'business_name' ? listings.businessName :
                      sort_by === 'date_listed' ? listings.dateListed :
                      sort_by === 'days_listed' ? listings.daysListed :
                      sort_by === 'updated_at' ? listings.updatedAt :
                      listings.createdAt;

    const orderByClause = sort_order === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(listings)
      .where(and(...conditions));

    // Apply pagination and get results
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: listings.id,
        workspaceId: listings.workspaceId,
        createdBy: listings.createdBy,
        assignedTo: listings.assignedTo,
        businessName: listings.businessName,
        industry: listings.industry,
        askingPrice: listings.askingPrice,
        cashFlowSde: listings.cashFlowSde,
        annualRevenue: listings.annualRevenue,
        status: listings.status,
        generalLocation: listings.generalLocation,
        yearEstablished: listings.yearEstablished,
        employees: listings.employees,
        ownerHoursWeek: listings.ownerHoursWeek,
        dateListed: listings.dateListed,
        daysListed: listings.daysListed,
        title: listings.title,
        description: listings.description,
        price: listings.price,
        teamVisibility: listings.teamVisibility,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
      })
      .from(listings)
      .where(and(...conditions))
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get a single listing by ID with optional details
   */
  static async getListingById(id: string, workspaceId: string, includeDetails = true) {
    const listing = await db
      .select()
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .limit(1);

    if (listing.length === 0) {
      throw new HTTPException(404, { message: "Listing not found" });
    }

    let details = null;
    if (includeDetails) {
      const detailsResult = await db
        .select()
        .from(listingDetails)
        .where(eq(listingDetails.listingId, id))
        .limit(1);
      
      details = detailsResult[0] || null;
    }

    return {
      ...listing[0],
      details,
    };
  }

  /**
   * Create a new listing
   */
  static async createListing(data: CreateListingData): Promise<any> {
    const now = new Date().toISOString();
    
    // Calculate days_listed if date_listed is provided
    const daysListed = data.date_listed ? this.calculateDaysListed(data.date_listed) : null;

    return await db.transaction(async (tx) => {
      // Create the main listing record
      const [listing] = await tx
        .insert(listings)
        .values({
          workspaceId: data.workspace_id,
          createdBy: data.created_by,
          assignedTo: data.assigned_to,
          businessName: data.business_name,
          industry: data.industry,
          askingPrice: data.asking_price?.toString(),
          cashFlowSde: data.cash_flow_sde?.toString(),
          annualRevenue: data.annual_revenue?.toString(),
          status: data.status || 'draft',
          generalLocation: data.general_location,
          yearEstablished: data.year_established,
          employees: data.employees,
          ownerHoursWeek: data.owner_hours_week,
          dateListed: data.date_listed,
          daysListed: daysListed,
          // Legacy fields
          title: data.title || data.business_name,
          description: data.description,
          price: data.asking_price?.toString(), // Copy to legacy field
          teamVisibility: data.team_visibility || 'all',
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      // Create listing details if provided
      let details = null;
      if (data.details) {
        [details] = await tx
          .insert(listingDetails)
          .values({
            listingId: listing.id,
            businessDescription: data.details.business_description,
            briefDescription: data.details.brief_description,
            financialDetails: data.details.financial_details || {},
            operations: data.details.operations || {},
            growthOpportunities: data.details.growth_opportunities || [],
            reasonForSale: data.details.reason_for_sale,
            trainingPeriod: data.details.training_period,
            supportType: data.details.support_type,
            financingAvailable: data.details.financing_available || false,
            equipmentHighlights: data.details.equipment_highlights || [],
            supplierRelationships: data.details.supplier_relationships,
            realEstateStatus: data.details.real_estate_status,
            leaseDetails: data.details.lease_details || {},
            createdAt: now,
            updatedAt: now,
          })
          .returning();
      }

      // Log initial status
      await tx
        .insert(listingStatusHistory)
        .values({
          listingId: listing.id,
          workspaceId: data.workspace_id,
          changedBy: data.created_by,
          fromStatus: null,
          toStatus: data.status || 'draft',
          reason: 'Initial listing creation',
          createdAt: now,
        });

      return {
        ...listing,
        details,
      };
    });
  }

  /**
   * Update an existing listing
   */
  static async updateListing(id: string, data: UpdateListingData, workspaceId: string): Promise<any> {
    const now = new Date().toISOString();

    // Check if listing exists
    const existingListing = await this.getListingById(id, workspaceId, false);
    
    // Calculate days_listed if date_listed is being updated
    const daysListed = data.date_listed ? this.calculateDaysListed(data.date_listed) : undefined;

    return await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: now,
      };

      // Update core fields
      if (data.business_name !== undefined) updateData.businessName = data.business_name;
      if (data.industry !== undefined) updateData.industry = data.industry;
      if (data.asking_price !== undefined) {
        updateData.askingPrice = data.asking_price?.toString();
        updateData.price = data.asking_price?.toString(); // Update legacy field
      }
      if (data.cash_flow_sde !== undefined) updateData.cashFlowSde = data.cash_flow_sde?.toString();
      if (data.annual_revenue !== undefined) updateData.annualRevenue = data.annual_revenue?.toString();
      if (data.general_location !== undefined) updateData.generalLocation = data.general_location;
      if (data.year_established !== undefined) updateData.yearEstablished = data.year_established;
      if (data.employees !== undefined) updateData.employees = data.employees;
      if (data.owner_hours_week !== undefined) updateData.ownerHoursWeek = data.owner_hours_week;
      if (data.date_listed !== undefined) {
        updateData.dateListed = data.date_listed;
        updateData.daysListed = daysListed;
      }
      if (data.assigned_to !== undefined) updateData.assignedTo = data.assigned_to;
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.team_visibility !== undefined) updateData.teamVisibility = data.team_visibility;

      // Update main listing
      const [updatedListing] = await tx
        .update(listings)
        .set(updateData)
        .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
        .returning();

      // Update or create details if provided
      let details = null;
      if (data.details) {
        const existingDetails = await tx
          .select()
          .from(listingDetails)
          .where(eq(listingDetails.listingId, id))
          .limit(1);

        if (existingDetails.length > 0) {
          // Update existing details
          [details] = await tx
            .update(listingDetails)
            .set({
              businessDescription: data.details.business_description,
              briefDescription: data.details.brief_description,
              financialDetails: data.details.financial_details,
              operations: data.details.operations,
              growthOpportunities: data.details.growth_opportunities,
              reasonForSale: data.details.reason_for_sale,
              trainingPeriod: data.details.training_period,
              supportType: data.details.support_type,
              financingAvailable: data.details.financing_available,
              equipmentHighlights: data.details.equipment_highlights,
              supplierRelationships: data.details.supplier_relationships,
              realEstateStatus: data.details.real_estate_status,
              leaseDetails: data.details.lease_details,
              updatedAt: now,
            })
            .where(eq(listingDetails.listingId, id))
            .returning();
        } else {
          // Create new details
          [details] = await tx
            .insert(listingDetails)
            .values({
              listingId: id,
              businessDescription: data.details.business_description,
              briefDescription: data.details.brief_description,
              financialDetails: data.details.financial_details || {},
              operations: data.details.operations || {},
              growthOpportunities: data.details.growth_opportunities || [],
              reasonForSale: data.details.reason_for_sale,
              trainingPeriod: data.details.training_period,
              supportType: data.details.support_type,
              financingAvailable: data.details.financing_available || false,
              equipmentHighlights: data.details.equipment_highlights || [],
              supplierRelationships: data.details.supplier_relationships,
              realEstateStatus: data.details.real_estate_status,
              leaseDetails: data.details.lease_details || {},
              createdAt: now,
              updatedAt: now,
            })
            .returning();
        }
      }

      return {
        ...updatedListing,
        details,
      };
    });
  }

  /**
   * Update listing status with history tracking
   */
  static async updateListingStatus(id: string, statusData: StatusChangeData): Promise<any> {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Get current listing
      const [currentListing] = await tx
        .select()
        .from(listings)
        .where(and(eq(listings.id, id), eq(listings.workspaceId, statusData.workspace_id)))
        .limit(1);

      if (!currentListing) {
        throw new HTTPException(404, { message: "Listing not found" });
      }

      const fromStatus = currentListing.status;

      // Update listing status
      const [updatedListing] = await tx
        .update(listings)
        .set({
          status: statusData.status,
          updatedAt: now,
        })
        .where(eq(listings.id, id))
        .returning();

      // Log status change
      const [statusChange] = await tx
        .insert(listingStatusHistory)
        .values({
          listingId: id,
          workspaceId: statusData.workspace_id,
          changedBy: statusData.changed_by,
          fromStatus: fromStatus,
          toStatus: statusData.status,
          reason: statusData.reason,
          notes: statusData.notes,
          createdAt: now,
        })
        .returning();

      return {
        listing: updatedListing,
        status_change: statusChange,
      };
    });
  }

  /**
   * Delete a listing
   */
  static async deleteListing(id: string, workspaceId: string): Promise<void> {
    const result = await db
      .delete(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .execute();

    // Check if any rows were affected by checking if we can find the listing
    const check = await db
      .select({ id: listings.id })
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .limit(1);

    if (check.length > 0) {
      throw new HTTPException(500, { message: "Failed to delete listing" });
    }
  }

  /**
   * Bulk create listings from CSV import
   */
  static async bulkCreateListings(listingsData: CreateListingData[]): Promise<any> {
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    for (let i = 0; i < listingsData.length; i++) {
      try {
        const listing = await this.createListing(listingsData[i]);
        results.created.push(listing);
      } catch (error) {
        results.failed.push({
          index: i,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: listingsData[i],
        });
      }
    }

    return results;
  }

  /**
   * Get listing status history
   */
  static async getListingStatusHistory(listingId: string, workspaceId: string) {
    // Verify listing exists and belongs to workspace
    await this.getListingById(listingId, workspaceId, false);

    const history = await db
      .select({
        id: listingStatusHistory.id,
        fromStatus: listingStatusHistory.fromStatus,
        toStatus: listingStatusHistory.toStatus,
        reason: listingStatusHistory.reason,
        notes: listingStatusHistory.notes,
        changedBy: listingStatusHistory.changedBy,
        createdAt: listingStatusHistory.createdAt,
        changedByName: sql<string>`concat(${userProfiles.firstName}, ' ', ${userProfiles.lastName})`,
      })
      .from(listingStatusHistory)
      .leftJoin(userProfiles, eq(listingStatusHistory.changedBy, userProfiles.userId))
      .where(and(
        eq(listingStatusHistory.listingId, listingId),
        eq(listingStatusHistory.workspaceId, workspaceId)
      ))
      .orderBy(desc(listingStatusHistory.createdAt));

    return history;
  }

  /**
   * Get listings analytics/statistics
   */
  static async getListingStats(workspaceId: string) {
    const [stats] = await db
      .select({
        total: count(),
        active: count(sql`CASE WHEN ${listings.status} = 'active' THEN 1 END`),
        underContract: count(sql`CASE WHEN ${listings.status} = 'under_contract' THEN 1 END`),
        sold: count(sql`CASE WHEN ${listings.status} = 'sold' THEN 1 END`),
        avgAskingPrice: sql<number>`avg(${listings.askingPrice}::numeric)`,
        avgDaysListed: sql<number>`avg(${listings.daysListed})`,
      })
      .from(listings)
      .where(eq(listings.workspaceId, workspaceId));

    return stats;
  }
} 