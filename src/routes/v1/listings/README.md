# Listings Module

This module provides a comprehensive CRUD API for managing business listings in a business broker application. It's designed to handle the specific needs of business brokers who need to manage confidential business sale listings.

## Features

### Core Functionality
- ✅ **Full CRUD Operations** - Create, Read, Update, Delete listings
- ✅ **Business-Specific Fields** - Industry, asking price, cash flow, SDE, etc.
- ✅ **Detailed Listing Information** - Separate table for extended business details
- ✅ **Status Change Tracking** - Complete audit trail of status changes
- ✅ **Bulk Import** - CSV/JSON bulk import capability
- ✅ **Auto-Calculated Fields** - Days listed automatically calculated
- ✅ **Advanced Filtering** - Filter by status, industry, price range, location
- ✅ **Search Functionality** - Full-text search across multiple fields
- ✅ **Pagination** - Efficient pagination for large datasets

### Security & Access Control
- ✅ **Workspace Isolation** - Listings are scoped to workspaces
- ✅ **Authentication Required** - All endpoints require authentication
- ✅ **Team Visibility Controls** - Configurable visibility settings

## API Endpoints

### Listings CRUD

#### `GET /v1/listings`
List all listings with filtering, search, and pagination.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 20) - Items per page
- `status` (string) - Filter by listing status
- `industry` (string) - Filter by industry
- `assigned_to` (UUID) - Filter by assigned user
- `min_price` (number) - Minimum asking price
- `max_price` (number) - Maximum asking price
- `location` (string) - Filter by location (partial match)
- `search` (string) - Search across business name, industry, description, location
- `sort_by` (string) - Sort field: `created_at`, `updated_at`, `asking_price`, `business_name`, `date_listed`, `days_listed`
- `sort_order` (string) - Sort order: `asc`, `desc`

#### `GET /v1/listings/{listing_id}`
Get a specific listing by ID.

**Query Parameters:**
- `include_details` (boolean, default: true) - Include listing details

#### `POST /v1/listings`
Create a new listing.

**Request Body:**
```json
{
  "business_name": "Example Restaurant",
  "industry": "Food & Beverage",
  "asking_price": 250000,
  "cash_flow_sde": 85000,
  "annual_revenue": 400000,
  "status": "draft",
  "general_location": "Downtown Seattle",
  "year_established": 2015,
  "employees": 8,
  "owner_hours_week": 50,
  "date_listed": "2024-01-15",
  "assigned_to": "user-uuid",
  "details": {
    "business_description": "Established restaurant with loyal customer base...",
    "brief_description": "Profitable restaurant in prime location",
    "financial_details": {
      "revenue_2023": 400000,
      "ebitda": 95000,
      "assets_included": ["Equipment", "Inventory", "Furniture"],
      "inventory_value": 25000
    },
    "operations": {
      "business_model": "Full-service restaurant with bar",
      "key_features": ["Prime location", "Liquor license", "Established brand"],
      "competitive_advantages": ["Only restaurant of this type in area"]
    },
    "growth_opportunities": ["Delivery service", "Catering", "Private events"],
    "reason_for_sale": "Owner retirement",
    "training_period": "4 weeks",
    "support_type": "Ongoing consultation for 6 months",
    "financing_available": true,
    "equipment_highlights": ["Professional kitchen equipment", "POS system"],
    "real_estate_status": "leased",
    "lease_details": {
      "lease_terms": "7 years remaining",
      "monthly_rent": 8500,
      "renewal_options": "2 additional 5-year terms"
    }
  }
}
```

#### `PUT /v1/listings/{listing_id}`
Update an existing listing.

#### `DELETE /v1/listings/{listing_id}`
Delete a listing.

### Bulk Operations

#### `POST /v1/listings/bulk`
Create multiple listings at once.

**Request Body:**
```json
{
  "listings": [
    {
      "business_name": "Business 1",
      "industry": "Retail",
      // ... other listing fields
    },
    {
      "business_name": "Business 2", 
      "industry": "Services",
      // ... other listing fields
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "created": [...], // Successfully created listings
    "failed": [       // Failed listings with error details
      {
        "index": 0,
        "error": "Validation error message",
        "data": {...}
      }
    ]
  }
}
```

### Status Management

#### `PATCH /v1/listings/{listing_id}/status`
Update listing status with audit trail.

**Request Body:**
```json
{
  "status": "active",
  "reason": "Listed on MLS",
  "notes": "Pricing approved by seller"
}
```

#### `GET /v1/listings/{listing_id}/status-history`
Get complete status change history for a listing.

## Database Schema

### `listings` Table
Core listing information:
- **Business Fields**: `business_name`, `industry`, `asking_price`, `cash_flow_sde`, `annual_revenue`
- **Status & Timing**: `status`, `date_listed`, `days_listed` (auto-calculated)
- **Location**: `general_location` (for confidentiality)
- **Business Details**: `year_established`, `employees`, `owner_hours_week`
- **Assignment**: `assigned_to`, `team_visibility`
- **Legacy Fields**: Maintained for backward compatibility

### `listing_details` Table
Extended business information:
- **Descriptions**: `business_description`, `brief_description`
- **Financial Details**: Structured JSONB with revenue, EBITDA, assets, etc.
- **Operations**: JSONB with business model, features, advantages
- **Sale Information**: Growth opportunities, reason for sale, training, support
- **Real Estate**: Status and lease details as JSONB

### `listing_status_history` Table
Complete audit trail:
- **Change Tracking**: `from_status`, `to_status`, `changed_by`, `reason`, `notes`
- **Metadata**: Extensible JSONB field for additional context
- **Timestamps**: When each change occurred

## Data Models

### Listing Status Values
- `draft` - Initial state, not yet active
- `active` - Listed and available
- `under_contract` - Offer accepted, in due diligence
- `sold` - Transaction completed
- `confidential` - Private/invite-only listing
- `expired` - Listing period ended
- `withdrawn` - Removed from market

### Real Estate Status Values
- `owned` - Business owns the real estate
- `leased` - Business leases the property
- `included` - Real estate included in sale
- `not_included` - Real estate not part of sale
- `negotiable` - Real estate terms negotiable

## Business Logic

### Auto-Calculated Fields

#### Days Listed
- Automatically calculated based on `date_listed`
- Updated via `ListingsService.updateDaysListedBatch()`
- Can be run as a scheduled job for all active listings

### Confidentiality Features
- `general_location` instead of specific address
- `brief_description` for public listing previews
- Full details only visible to authorized users

### Team Visibility
- `all` - Visible to entire workspace team
- `assigned_only` - Only visible to assigned user and admins
- `admins_only` - Only visible to workspace administrators

## Usage Examples

### Creating a Listing
```typescript
import { ListingsService } from './listings.service';

const listing = await ListingsService.createListing({
  business_name: "Tech Startup",
  industry: "Technology",
  asking_price: 500000,
  workspace_id: "workspace-uuid",
  created_by: "user-uuid",
  details: {
    business_description: "SaaS platform with recurring revenue...",
    financial_details: {
      revenue_2023: 300000,
      ebitda: 120000
    }
  }
});
```

### Bulk Import from CSV
```typescript
const csvData = [
  {
    business_name: "Restaurant A",
    industry: "Food Service",
    asking_price: 200000,
    // ...
  },
  // ... more listings
];

const result = await ListingsService.bulkCreateListings(csvData);
console.log(`Created: ${result.created.length}, Failed: ${result.failed.length}`);
```

### Status Tracking
```typescript
await ListingsService.updateListingStatus(listingId, {
  status: 'under_contract',
  reason: 'Accepted offer from qualified buyer',
  notes: 'Buyer has proof of funds, due diligence period is 30 days',
  changed_by: userId,
  workspace_id: workspaceId
});
```

### Advanced Filtering
```typescript
const listings = await ListingsService.getListings({
  status: 'active',
  industry: 'Technology',
  min_price: 100000,
  max_price: 1000000,
  location: 'Seattle',
  search: 'SaaS recurring revenue',
  sort_by: 'asking_price',
  sort_order: 'desc'
}, workspaceId);
```

## Testing

Tests are located in `listings.test.ts`. To run the listings module tests:

```bash
npm test src/routes/v1/listings/listings.test.ts
```

Note: Tests require proper authentication setup and database configuration.

## Migration

The database migration `0004_clumsy_nitro.sql` includes:
- New `listing_details` table
- New `listing_status_history` table  
- Additional columns in `listings` table
- Proper foreign key constraints and indexes

Run the migration:
```bash
npx drizzle-kit push
```

## Performance Considerations

### Indexes
- All filter-able fields have database indexes
- Composite indexes for common query patterns
- Foreign key indexes for join performance

### Pagination
- Efficient offset-based pagination
- Total count calculation optimized
- Configurable page size limits

### Bulk Operations
- Transactional bulk inserts
- Error handling per record
- Memory-efficient processing

## Security Considerations

### Data Privacy
- No sensitive financial data in brief descriptions
- Location information generalized
- Access controlled by workspace membership

### Input Validation
- All inputs validated via Zod schemas
- SQL injection prevention via parameterized queries
- File upload restrictions (for future CSV import UI)

### Audit Trail
- Complete status change history
- User attribution for all changes
- Immutable historical records 