import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { createErrorResponse } from "@/middlewares/validation";
import { getCurrentUser, getCurrentWorkspace } from "@/middlewares/auth";
import { ListingsService } from "./listings.service";
import type { 
  listListingsRoute,
  getListingRoute,
  createListingRoute,
  updateListingRoute,
  deleteListingRoute,
  bulkCreateListingsRoute,
  updateListingStatusRoute,
  getListingStatusHistoryRoute
} from "./listings.routes";

export const listListings: AppRouteHandler<typeof listListingsRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const query = c.req.valid("query");

    const filters = {
      page: query.page,
      limit: query.limit,
      status: query.status,
      industry: query.industry,
      assigned_to: query.assigned_to,
      min_price: query.min_price,
      max_price: query.max_price,
      location: query.location,
      sort_by: query.sort_by,
      sort_order: query.sort_order,
      search: query.search,
    };

    const result = await ListingsService.getListings(filters, workspace.id);

    return c.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error listing listings:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listings',
      c.req.path
    ), 500);
  }
};

export const getListing: AppRouteHandler<typeof getListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listing_id } = c.req.valid("param");
    const { include_details } = c.req.valid("query");

    const listing = await ListingsService.getListingById(
      listing_id, 
      workspace.id, 
      include_details === 'true'
    );

    return c.json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error getting listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing',
      c.req.path
    ), 500);
  }
};

export const createListing: AppRouteHandler<typeof createListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const body = c.req.valid("json");

    const listingData = {
      ...body,
      workspace_id: workspace.id,
      created_by: user.id,
    };

    const listing = await ListingsService.createListing(listingData);

    return c.json({
      success: true,
      data: listing,
    }, 201);
  } catch (error) {
    console.error('Error creating listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to create listing',
      c.req.path
    ), 500);
  }
};

export const updateListing: AppRouteHandler<typeof updateListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listing_id } = c.req.valid("param");
    const body = c.req.valid("json");

    const listing = await ListingsService.updateListing(listing_id, body, workspace.id);

    return c.json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error updating listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update listing',
      c.req.path
    ), 500);
  }
};

export const deleteListing: AppRouteHandler<typeof deleteListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listing_id } = c.req.valid("param");

    await ListingsService.deleteListing(listing_id, workspace.id);

    return c.json({
      success: true,
      message: 'Listing deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DELETE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to delete listing',
      c.req.path
    ), 500);
  }
};

export const bulkCreateListings: AppRouteHandler<typeof bulkCreateListingsRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listings } = c.req.valid("json");

    // Add workspace and user context to each listing
    const listingsWithContext = listings.map((listing: any) => ({
      ...listing,
      workspace_id: workspace.id,
      created_by: user.id,
    }));

    const result = await ListingsService.bulkCreateListings(listingsWithContext);

    return c.json({
      success: true,
      data: result,
    }, 201);
  } catch (error) {
    console.error('Error bulk creating listings:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'BULK_CREATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to bulk create listings',
      c.req.path
    ), 500);
  }
};

export const updateListingStatus: AppRouteHandler<typeof updateListingStatusRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listing_id } = c.req.valid("param");
    const body = c.req.valid("json");

    const statusData = {
      status: body.status,
      reason: body.reason,
      notes: body.notes,
      changed_by: user.id,
      workspace_id: workspace.id,
    };

    const result = await ListingsService.updateListingStatus(listing_id, statusData);

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error updating listing status:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'STATUS_UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update listing status',
      c.req.path
    ), 500);
  }
};

export const getListingStatusHistory: AppRouteHandler<typeof getListingStatusHistoryRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listing_id } = c.req.valid("param");

    const history = await ListingsService.getListingStatusHistory(listing_id, workspace.id);

    return c.json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error('Error getting listing status history:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'HISTORY_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing status history',
      c.req.path
    ), 500);
  }
}; 