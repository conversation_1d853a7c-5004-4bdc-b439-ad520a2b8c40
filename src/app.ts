import configureOpenAPI from "@/lib/configure-open-api";
import createApp from "@/lib/create-app";
import index from "@/routes/index.route";
import auth from "@/routes/v1/auth/auth.module";
import files from "@/routes/v1/files/files.module";
import users from "@/routes/v1/users/users.module";
import listings from "@/routes/v1/listings/listings.module";
import logs from "@/routes/v1/logs/logs.module";

const app = createApp();

configureOpenAPI(app);

const routes = [
  index,
  auth,
  users,
  files,
  listings,
  logs,
] as const;

routes.forEach((route) => {
  app.route("/", route);
});

export type AppType = typeof routes[number];

export default app;
