LOG_LEVEL=debug

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration (using Docker Postgres)
# Database connection string for Drizzle ORM
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Supabase Configuration (placeholder - update with your values)
SUPABASE_URL=http://supabasekong-ckk88cosw0o4oc4ogww4sggw.***************.sslip.io
SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc1MjkwNjMwMCwiZXhwIjo0OTA4NTc5OTAwLCJyb2xlIjoiYW5vbiJ9.v-gUnIVBm_HKNNNtzQlwCLTVe6sQHMJMokDo96fz4oU
SUPABASE_SERVICE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc1MjkwNjMwMCwiZXhwIjo0OTA4NTc5OTAwLCJyb2xlIjoic2VydmljZV9yb2xlIn0.WH8ERglSfgEXIEYIbn7lTXGnbdK14IfS8csaP4nGKR4

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://default:kMDyGvus0T1ETP9SZM83oGypECbnKmYB6QhvZeoiJdxe2HjRAZ4i7bn054HN0ZrG@***************:6666/0
