# Hono OpenAPI Starter Template

A production-ready starter template for building REST APIs with Hono.js, OpenAPI documentation, and modern TypeScript development tools.

## 🚀 Features

- **[Hono.js](https://hono.dev/)** - Ultrafast web framework
- **OpenAPI 3.1** - Auto-generated API documentation with [@hono/zod-openapi](https://github.com/honojs/middleware/tree/main/packages/zod-openapi)
- **[Drizzle ORM](https://orm.drizzle.team/)** - Type-safe database operations
- **[Supabase](https://supabase.com/)** - Database and authentication (PostgreSQL)
- **TypeScript** - Full type safety
- **Zod** - Runtime type validation
- **Vitest** - Modern testing framework
- **ESLint** - Code quality and consistency
- **Hot Reload** - Fast development with Bun

## 📋 Prerequisites

- [Bun](https://bun.sh/) >= 1.0.0
- [Supabase](https://supabase.com/) project (for database)

## 🛠️ Getting Started

### 1. Use This Template

Click the "Use this template" button above, or:

```bash
# Clone the repository
git clone <your-repo-url>
cd <your-project-name>

# Install dependencies
bun install
```

### 2. Environment Setup

Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database"
SUPABASE_URL="your-supabase-url"
SUPABASE_ANON_KEY="your-supabase-anon-key"

# Server
PORT=3000
NODE_ENV=development
```

### 3. Database Setup

```bash
# Generate database migrations
bun run db:generate

# Run migrations
bun run db:migrate

# Or push schema directly (for development)
bun run db:push
```

### 4. Start Development

```bash
# Start the development server
bun run dev

# The API will be available at http://localhost:3000
# OpenAPI documentation at http://localhost:3000/doc
```

## 📚 Project Structure

```
src/
├── app.ts                 # Main application setup
├── index.ts              # Server entry point
├── env.ts                # Environment validation
├── db/
│   ├── index.ts          # Database connection
│   ├── schema.ts         # Database schema
│   ├── relations.ts      # Table relations
│   └── migrations/       # Database migrations
├── lib/
│   ├── configure-open-api.ts  # OpenAPI configuration
│   ├── create-app.ts     # Hono app factory
│   ├── constants.ts      # App constants
│   ├── supabase.ts       # Supabase client
│   └── types.ts          # Shared types
├── middlewares/
│   ├── auth.ts           # Authentication middleware
│   └── pino-logger.ts    # Logging middleware
└── routes/
    ├── index.route.ts    # Root routes
    ├── auth/             # Authentication routes
    │   ├── auth.controller.ts
    │   ├── auth.service.ts
    │   ├── auth.module.ts
    │   └── auth.test.ts
    └── examples/         # Example CRUD routes
        ├── examples.controller.ts
        ├── examples.service.ts
        ├── examples.module.ts
        └── examples.test.ts
```

## 🧪 Testing

```bash
# Run tests
bun run test

# Run tests in watch mode
bun run test --watch
```

## 🚀 Production Deployment

### Build

```bash
# Build for production
bun run build

# Start production server
bun run start
```

### Docker (Optional)

```dockerfile
FROM oven/bun:1 as base
WORKDIR /usr/src/app

# Install dependencies
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Build
RUN bun run build

# Production stage
FROM oven/bun:1-slim
WORKDIR /usr/src/app
COPY --from=base /usr/src/app/dist ./dist
COPY --from=base /usr/src/app/node_modules ./node_modules
COPY --from=base /usr/src/app/package.json ./

EXPOSE 3000
CMD ["bun", "run", "start"]
```

## 📖 API Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:3000/doc`
- **OpenAPI JSON**: `http://localhost:3000/doc/openapi.json`

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `bun run dev` | Start development server with hot reload |
| `bun run build` | Build for production |
| `bun run start` | Start production server |
| `bun run test` | Run tests |
| `bun run db:generate` | Generate database migrations |
| `bun run db:migrate` | Run database migrations |
| `bun run db:push` | Push schema to database |
| `bun run db:pull` | Pull schema from database |
| `bun run typecheck` | Run TypeScript type checking |
| `bun run lint` | Run ESLint |
| `bun run lint:fix` | Fix ESLint issues |

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Hono.js](https://hono.dev/) - The amazing web framework
- [Drizzle ORM](https://orm.drizzle.team/) - Type-safe database toolkit
- [Supabase](https://supabase.com/) - Open source Firebase alternative
